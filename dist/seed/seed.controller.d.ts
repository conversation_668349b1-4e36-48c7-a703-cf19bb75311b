import { SeedService } from './seed.service';
export declare class SeedController {
    private readonly seedService;
    constructor(seedService: SeedService);
    seedAll(): Promise<{
        success: boolean;
        message: string;
        data: {
            entityTypes: number;
            appSettings: number;
            categories: number;
            tags: number;
            features: number;
            entities: number;
            adminUser: number;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getStatus(): Promise<{
        entityTypes: number;
        appSettings: number;
        users: number;
        entities: number;
        error?: undefined;
    } | {
        error: any;
        entityTypes?: undefined;
        appSettings?: undefined;
        users?: undefined;
        entities?: undefined;
    }>;
    seedEntitiesOnly(): Promise<{
        success: boolean;
        message: string;
        data: {
            entity: string;
            adminUser: string | null;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    checkSchema(): Promise<{
        tables: unknown;
        entityColumns: unknown;
        error?: undefined;
    } | {
        error: any;
        tables?: undefined;
        entityColumns?: undefined;
    }>;
}
