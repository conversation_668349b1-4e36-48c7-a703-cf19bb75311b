import { PrismaService } from '../prisma/prisma.service';
export declare class SeedService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    seedAll(): Promise<{
        success: boolean;
        message: string;
        data: {
            entityTypes: number;
            appSettings: number;
            categories: number;
            tags: number;
            features: number;
            entities: number;
            adminUser: number;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getStatus(): Promise<{
        entityTypes: number;
        appSettings: number;
        users: number;
        entities: number;
        error?: undefined;
    } | {
        error: any;
        entityTypes?: undefined;
        appSettings?: undefined;
        users?: undefined;
        entities?: undefined;
    }>;
    private seedEntityTypes;
    private seedAppSettings;
    private seedCategories;
    private seedTags;
    private seedFeatures;
    private seedSampleData;
    seedEntitiesOnly(): Promise<{
        success: boolean;
        message: string;
        data: {
            entity: string;
            adminUser: string | null;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    checkSchema(): Promise<{
        tables: unknown;
        entityColumns: unknown;
        error?: undefined;
    } | {
        error: any;
        tables?: undefined;
        entityColumns?: undefined;
    }>;
}
