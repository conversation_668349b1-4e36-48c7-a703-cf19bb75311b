"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveProfileService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const user_preferences_service_1 = require("./user-preferences.service");
const activity_logger_service_1 = require("../common/activity-logger.service");
let ComprehensiveProfileService = class ComprehensiveProfileService {
    constructor(prismaService, userPreferencesService, activityLoggerService) {
        this.prismaService = prismaService;
        this.userPreferencesService = userPreferencesService;
        this.activityLoggerService = activityLoggerService;
    }
    async getComprehensiveProfile(userId) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new Error('User not found');
        }
        const preferences = await this.userPreferencesService.getUserPreferences(userId);
        const recentActivities = await this.activityLoggerService.getRecentActivities(userId, 10);
        const userProfile = {
            id: user.id,
            authUserId: user.authUserId,
            username: user.username || undefined,
            displayName: user.displayName || undefined,
            email: user.email,
            role: user.role,
            status: user.status,
            technicalLevel: user.technicalLevel || undefined,
            profilePictureUrl: user.profilePictureUrl || undefined,
            bio: user.bio || undefined,
            social_links: user.socialLinks,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLogin: user.lastLogin || undefined,
        };
        const stats = {
            bookmarks_count: user.bookmarksCount,
            reviews_count: user.reviewsCount,
            tools_submitted: user.toolsSubmitted,
            tools_approved: user.toolsApproved,
            requests_made: user.requestsMade,
            requests_fulfilled: user.requestsFulfilled,
            reputation_score: user.reputationScore,
            member_since: user.createdAt,
        };
        const recent_activity = recentActivities.map(activity => ({
            id: activity.id,
            type: activity.type,
            description: activity.description,
            entity_id: activity.entityId,
            entity_name: activity.entityName,
            entity_slug: activity.entitySlug,
            created_at: activity.createdAt,
        }));
        return {
            user: userProfile,
            preferences,
            stats,
            recent_activity,
        };
    }
    async logActivity(userId, type, description, entityId, entityName, entitySlug) {
        await this.activityLoggerService.logActivity({
            userId,
            type,
            description,
            entityId,
            entityName,
            entitySlug,
        });
    }
};
exports.ComprehensiveProfileService = ComprehensiveProfileService;
exports.ComprehensiveProfileService = ComprehensiveProfileService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        user_preferences_service_1.UserPreferencesService,
        activity_logger_service_1.ActivityLoggerService])
], ComprehensiveProfileService);
//# sourceMappingURL=comprehensive-profile.service.js.map