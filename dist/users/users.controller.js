"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const user_preferences_service_1 = require("./user-preferences.service");
const tool_request_service_1 = require("./tool-request.service");
const user_submitted_tools_service_1 = require("./user-submitted-tools.service");
const comprehensive_profile_service_1 = require("./comprehensive-profile.service");
const update_profile_dto_1 = require("./dto/update-profile.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const api_response_dto_1 = require("../common/dto/api-response.dto");
const user_preferences_dto_1 = require("./dto/user-preferences.dto");
const tool_request_dto_1 = require("./dto/tool-request.dto");
const user_submitted_tools_dto_1 = require("./dto/user-submitted-tools.dto");
const swagger_1 = require("@nestjs/swagger");
const user_profile_response_dto_1 = require("./dto/user-profile-response.dto");
let UserController = class UserController {
    constructor(userService, userPreferencesService, toolRequestService, userSubmittedToolsService, comprehensiveProfileService) {
        this.userService = userService;
        this.userPreferencesService = userPreferencesService;
        this.toolRequestService = toolRequestService;
        this.userSubmittedToolsService = userSubmittedToolsService;
        this.comprehensiveProfileService = comprehensiveProfileService;
    }
    async getMyProfile(user) {
        const profile = await this.userService.findProfileById(user.id);
        if (!profile) {
            throw new common_1.NotFoundException('User profile not found. User might not exist in public table or JWT strategy is incorrect.');
        }
        return {
            id: profile.id,
            authUserId: profile.authUserId,
            email: profile.email,
            username: profile.username,
            displayName: profile.displayName,
            profilePictureUrl: profile.profilePictureUrl,
            bio: profile.bio,
            status: profile.status,
            role: profile.role,
            createdAt: profile.createdAt,
            updatedAt: profile.updatedAt,
            lastLoginAt: profile.lastLogin,
        };
    }
    async getComprehensiveProfile(user) {
        const profileData = await this.comprehensiveProfileService.getComprehensiveProfile(user.id);
        return api_response_dto_1.ApiResponseDto.success(profileData);
    }
    async updateMyProfile(user, updateProfileDto) {
        const updatedProfile = await this.userService.updateProfile(user.id, updateProfileDto);
        return {
            id: updatedProfile.id,
            authUserId: updatedProfile.authUserId,
            email: updatedProfile.email,
            username: updatedProfile.username,
            displayName: updatedProfile.displayName,
            profilePictureUrl: updatedProfile.profilePictureUrl,
            bio: updatedProfile.bio,
            status: updatedProfile.status,
            role: updatedProfile.role,
            createdAt: updatedProfile.createdAt,
            updatedAt: updatedProfile.updatedAt,
            lastLoginAt: updatedProfile.lastLogin,
        };
    }
    async getMyPreferences(user) {
        return this.userPreferencesService.getUserPreferences(user.id);
    }
    async updateMyPreferences(user, updateDto) {
        return this.userPreferencesService.updateUserPreferences(user.id, updateDto);
    }
    async getMyToolRequests(user, listDto) {
        return this.toolRequestService.getUserToolRequests(user.id, listDto);
    }
    async getMySubmittedTools(user, listDto) {
        return this.userSubmittedToolsService.getUserSubmittedTools(user.id, listDto);
    }
    async softDeleteMyAccount(user) {
        await this.userService.softDeleteUser(user.id, user.authUserId);
        return { message: 'Account successfully scheduled for deletion. All related data will be processed according to our retention policies.' };
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current authenticated user\'s profile",
        description: "Retrieves the detailed profile information for the currently logged-in user."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Current user\'s profile retrieved successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'User profile not found in the database. This might indicate an issue with data consistency or if the user was recently deleted.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyProfile", null);
__decorate([
    (0, common_1.Get)('me/profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get comprehensive user profile",
        description: "Retrieves complete user profile including user data, preferences, statistics, and recent activity."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Comprehensive profile retrieved successfully.", type: api_response_dto_1.ApiResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'User profile not found.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getComprehensiveProfile", null);
__decorate([
    (0, common_1.Put)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Update current authenticated user\'s profile",
        description: "Allows the currently logged-in user to update their profile information. Certain fields like email, role, or status might be restricted or handled by separate processes."
    }),
    (0, swagger_1.ApiBody)({ type: update_profile_dto_1.UpdateProfileDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "User profile updated successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., username format, length constraints) or username conflict if attempting to change to an existing username.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'User not found during update attempt.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_profile_dto_1.UpdateProfileDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateMyProfile", null);
__decorate([
    (0, common_1.Get)('me/preferences'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current authenticated user\'s preferences",
        description: "Retrieves all user preferences including notifications, privacy, display, and content settings."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "User preferences retrieved successfully.", type: user_preferences_dto_1.UserPreferencesResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyPreferences", null);
__decorate([
    (0, common_1.Put)('me/preferences'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Update current authenticated user\'s preferences",
        description: "Allows the currently logged-in user to update their preferences including notifications, privacy, display, and content settings."
    }),
    (0, swagger_1.ApiBody)({ type: user_preferences_dto_1.UpdateUserPreferencesDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "User preferences updated successfully.", type: user_preferences_dto_1.UserPreferencesResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data for preferences.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_preferences_dto_1.UpdateUserPreferencesDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateMyPreferences", null);
__decorate([
    (0, common_1.Get)('me/tool-requests'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current user's tool requests",
        description: "Retrieves all tool requests made by the currently logged-in user with pagination."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Tool requests retrieved successfully.", type: tool_request_dto_1.PaginatedToolRequestsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, tool_request_dto_1.ListToolRequestsDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyToolRequests", null);
__decorate([
    (0, common_1.Get)('me/submitted-tools'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current user's submitted tools",
        description: "Retrieves all tools submitted by the currently logged-in user with their review status and entity details."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Submitted tools retrieved successfully.", type: user_submitted_tools_dto_1.PaginatedUserSubmittedToolsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_submitted_tools_dto_1.ListUserSubmittedToolsDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMySubmittedTools", null);
__decorate([
    (0, common_1.Delete)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: "Soft delete current authenticated user\'s account",
        description: "Marks the current user\'s account for deletion. The actual deletion and data purging process might be asynchronous and subject to retention policies. This action requires the user\'s current JWT."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Account successfully scheduled for deletion.', schema: { example: { message: 'Account successfully scheduled for deletion. ...' } } }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Failed to process account deletion due to an internal error.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "softDeleteMyAccount", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('Current User'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UserService,
        user_preferences_service_1.UserPreferencesService,
        tool_request_service_1.ToolRequestService,
        user_submitted_tools_service_1.UserSubmittedToolsService,
        comprehensive_profile_service_1.ComprehensiveProfileService])
], UserController);
//# sourceMappingURL=users.controller.js.map