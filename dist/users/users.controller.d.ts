import { UserService } from './users.service';
import { UserPreferencesService } from './user-preferences.service';
import { ToolRequestService } from './tool-request.service';
import { UserSubmittedToolsService } from './user-submitted-tools.service';
import { ComprehensiveProfileService } from './comprehensive-profile.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ApiResponseDto } from '../common/dto/api-response.dto';
import { UpdateUserPreferencesDto, UserPreferencesResponseDto } from './dto/user-preferences.dto';
import { ListToolRequestsDto, PaginatedToolRequestsResponseDto } from './dto/tool-request.dto';
import { ListUserSubmittedToolsDto, PaginatedUserSubmittedToolsResponseDto } from './dto/user-submitted-tools.dto';
import { ComprehensiveProfileResponseDto } from './dto/comprehensive-profile.dto';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { User as PrismaUser } from '../../generated/prisma';
export declare class UserController {
    private readonly userService;
    private readonly userPreferencesService;
    private readonly toolRequestService;
    private readonly userSubmittedToolsService;
    private readonly comprehensiveProfileService;
    constructor(userService: UserService, userPreferencesService: UserPreferencesService, toolRequestService: ToolRequestService, userSubmittedToolsService: UserSubmittedToolsService, comprehensiveProfileService: ComprehensiveProfileService);
    getMyProfile(user: PrismaUser): Promise<UserProfileResponseDto>;
    getComprehensiveProfile(user: PrismaUser): Promise<ApiResponseDto<ComprehensiveProfileResponseDto>>;
    updateMyProfile(user: PrismaUser, updateProfileDto: UpdateProfileDto): Promise<UserProfileResponseDto>;
    getMyPreferences(user: PrismaUser): Promise<UserPreferencesResponseDto>;
    updateMyPreferences(user: PrismaUser, updateDto: UpdateUserPreferencesDto): Promise<UserPreferencesResponseDto>;
    getMyToolRequests(user: PrismaUser, listDto: ListToolRequestsDto): Promise<PaginatedToolRequestsResponseDto>;
    getMySubmittedTools(user: PrismaUser, listDto: ListUserSubmittedToolsDto): Promise<PaginatedUserSubmittedToolsResponseDto>;
    softDeleteMyAccount(user: PrismaUser): Promise<{
        message: string;
    }>;
}
