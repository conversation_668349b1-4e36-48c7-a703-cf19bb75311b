import { UserRole, UserStatus, TechnicalLevel, ProfileActivityType } from '../../../generated/prisma';
import { UserPreferencesResponseDto } from './user-preferences.dto';
import { SocialLinksDto } from './social-links.dto';
export declare class UserStatsDto {
    bookmarks_count: number;
    reviews_count: number;
    tools_submitted: number;
    tools_approved: number;
    requests_made: number;
    requests_fulfilled: number;
    reputation_score: number;
    member_since: Date;
}
export declare class ProfileActivityDto {
    id: string;
    type: ProfileActivityType;
    description: string;
    entity_id?: string;
    entity_name?: string;
    entity_slug?: string;
    created_at: Date;
}
export declare class UserProfileDto {
    id: string;
    authUserId: string;
    username?: string;
    displayName?: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    technicalLevel?: TechnicalLevel;
    profilePictureUrl?: string;
    bio?: string;
    social_links?: SocialLinksDto;
    createdAt: Date;
    updatedAt: Date;
    lastLogin?: Date;
}
export declare class ComprehensiveProfileResponseDto {
    user: UserProfileDto;
    preferences: UserPreferencesResponseDto;
    stats: UserStatsDto;
    recent_activity: ProfileActivityDto[];
}
