"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveProfileResponseDto = exports.UserProfileDto = exports.ProfileActivityDto = exports.UserStatsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const prisma_1 = require("../../../generated/prisma");
const user_preferences_dto_1 = require("./user-preferences.dto");
const social_links_dto_1 = require("./social-links.dto");
class UserStatsDto {
}
exports.UserStatsDto = UserStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of bookmarked entities' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "bookmarks_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of reviews written' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "reviews_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of tools submitted' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "tools_submitted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of tools approved' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "tools_approved", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of tool requests made' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "requests_made", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of requests fulfilled' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "requests_fulfilled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User reputation score' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "reputation_score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Date when user joined' }),
    __metadata("design:type", Date)
], UserStatsDto.prototype, "member_since", void 0);
class ProfileActivityDto {
}
exports.ProfileActivityDto = ProfileActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier for the activity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: prisma_1.ProfileActivityType, description: 'Type of activity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the activity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Entity ID if activity is related to an entity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "entity_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Entity name if activity is related to an entity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "entity_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Entity slug if activity is related to an entity' }),
    __metadata("design:type", String)
], ProfileActivityDto.prototype, "entity_slug", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the activity occurred' }),
    __metadata("design:type", Date)
], ProfileActivityDto.prototype, "created_at", void 0);
class UserProfileDto {
}
exports.UserProfileDto = UserProfileDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Auth User ID' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "authUserId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Username' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Display name' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email address' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: prisma_1.UserRole, description: 'User role' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: prisma_1.UserStatus, description: 'User status' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.TechnicalLevel, description: 'Technical level' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "technicalLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Profile picture URL' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "profilePictureUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'User biography' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "bio", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Social media links',
        type: social_links_dto_1.SocialLinksDto,
        example: {
            website: 'https://johndoe.com',
            twitter: 'https://twitter.com/johndoe',
            linkedin: 'https://linkedin.com/in/johndoe',
            github: 'https://github.com/johndoe'
        }
    }),
    __metadata("design:type", social_links_dto_1.SocialLinksDto)
], UserProfileDto.prototype, "social_links", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account creation date' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last login date' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "lastLogin", void 0);
class ComprehensiveProfileResponseDto {
}
exports.ComprehensiveProfileResponseDto = ComprehensiveProfileResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User profile information', type: UserProfileDto }),
    __metadata("design:type", UserProfileDto)
], ComprehensiveProfileResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User preferences', type: user_preferences_dto_1.UserPreferencesResponseDto }),
    __metadata("design:type", user_preferences_dto_1.UserPreferencesResponseDto)
], ComprehensiveProfileResponseDto.prototype, "preferences", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User statistics', type: UserStatsDto }),
    __metadata("design:type", UserStatsDto)
], ComprehensiveProfileResponseDto.prototype, "stats", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Recent user activities', type: [ProfileActivityDto] }),
    __metadata("design:type", Array)
], ComprehensiveProfileResponseDto.prototype, "recent_activity", void 0);
//# sourceMappingURL=comprehensive-profile.dto.js.map