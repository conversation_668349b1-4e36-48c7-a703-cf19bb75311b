import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole, UserStatus, TechnicalLevel, ProfileActivityType } from '../../../generated/prisma';
import { UserPreferencesResponseDto } from './user-preferences.dto';
import { SocialLinksDto } from './social-links.dto';

export class UserStatsDto {
  @ApiProperty({ description: 'Number of bookmarked entities' })
  bookmarks_count: number;

  @ApiProperty({ description: 'Number of reviews written' })
  reviews_count: number;

  @ApiProperty({ description: 'Number of tools submitted' })
  tools_submitted: number;

  @ApiProperty({ description: 'Number of tools approved' })
  tools_approved: number;

  @ApiProperty({ description: 'Number of tool requests made' })
  requests_made: number;

  @ApiProperty({ description: 'Number of requests fulfilled' })
  requests_fulfilled: number;

  @ApiProperty({ description: 'User reputation score' })
  reputation_score: number;

  @ApiProperty({ description: 'Date when user joined' })
  member_since: Date;
}

export class ProfileActivityDto {
  @ApiProperty({ description: 'Unique identifier for the activity' })
  id: string;

  @ApiProperty({ enum: ProfileActivityType, description: 'Type of activity' })
  type: ProfileActivityType;

  @ApiProperty({ description: 'Description of the activity' })
  description: string;

  @ApiPropertyOptional({ description: 'Entity ID if activity is related to an entity' })
  entity_id?: string;

  @ApiPropertyOptional({ description: 'Entity name if activity is related to an entity' })
  entity_name?: string;

  @ApiPropertyOptional({ description: 'Entity slug if activity is related to an entity' })
  entity_slug?: string;

  @ApiProperty({ description: 'Timestamp when the activity occurred' })
  created_at: Date;
}

export class UserProfileDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'Auth User ID' })
  authUserId: string;

  @ApiPropertyOptional({ description: 'Username' })
  username?: string;

  @ApiPropertyOptional({ description: 'Display name' })
  displayName?: string;

  @ApiProperty({ description: 'Email address' })
  email: string;

  @ApiProperty({ enum: UserRole, description: 'User role' })
  role: UserRole;

  @ApiProperty({ enum: UserStatus, description: 'User status' })
  status: UserStatus;

  @ApiPropertyOptional({ enum: TechnicalLevel, description: 'Technical level' })
  technicalLevel?: TechnicalLevel;

  @ApiPropertyOptional({ description: 'Profile picture URL' })
  profilePictureUrl?: string;

  @ApiPropertyOptional({ description: 'User biography' })
  bio?: string;

  @ApiPropertyOptional({
    description: 'Social media links',
    type: SocialLinksDto,
    example: {
      website: 'https://johndoe.com',
      twitter: 'https://twitter.com/johndoe',
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe'
    }
  })
  social_links?: SocialLinksDto;

  @ApiProperty({ description: 'Account creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Last login date' })
  lastLogin?: Date;
}

export class ComprehensiveProfileResponseDto {
  @ApiProperty({ description: 'User profile information', type: UserProfileDto })
  user: UserProfileDto;

  @ApiProperty({ description: 'User preferences', type: UserPreferencesResponseDto })
  preferences: UserPreferencesResponseDto;

  @ApiProperty({ description: 'User statistics', type: UserStatsDto })
  stats: UserStatsDto;

  @ApiProperty({ description: 'Recent user activities', type: [ProfileActivityDto] })
  recent_activity: ProfileActivityDto[];
}
