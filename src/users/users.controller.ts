import { Controller, Get, Post, Body, Patch, Param, Delete, Put, UseGuards, NotFoundException, HttpCode, HttpStatus, Query, ValidationPipe } from '@nestjs/common';
import { UserService } from './users.service';
import { UserPreferencesService } from './user-preferences.service';
import { ToolRequestService } from './tool-request.service';
import { UserSubmittedToolsService } from './user-submitted-tools.service';
import { ComprehensiveProfileService } from './comprehensive-profile.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'; // Adjust path if needed
import { GetUser } from '../auth/decorators/get-user.decorator'; // Adjust path if needed
import { ApiResponseDto } from '../common/dto/api-response.dto';
// Import the User type from the generated Prisma client
import { User as UserModel, UserNotificationSettings as UserNotificationSettingsModel, UserRole, UserStatus } from '../../generated/prisma'; // Use 'as' to avoid naming conflict with @GetUser parameter
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { UpdateUserPreferencesDto, UserPreferencesResponseDto } from './dto/user-preferences.dto';
import { CreateToolRequestDto, ToolRequestResponseDto, ListToolRequestsDto, PaginatedToolRequestsResponseDto } from './dto/tool-request.dto';
import { UserSubmittedToolResponseDto, ListUserSubmittedToolsDto, PaginatedUserSubmittedToolsResponseDto } from './dto/user-submitted-tools.dto';
import { ComprehensiveProfileResponseDto } from './dto/comprehensive-profile.dto';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { UserNotificationSettingsResponseDto } from './dto/user-notification-settings-response.dto';
import { User as PrismaUser } from '../../generated/prisma';

@ApiTags('Current User') // Changed tag for clarity
@ApiBearerAuth() 
@Controller('users') // Base path usually /users, specific to current user is /users/me
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly userPreferencesService: UserPreferencesService,
    private readonly toolRequestService: ToolRequestService,
    private readonly userSubmittedToolsService: UserSubmittedToolsService,
    private readonly comprehensiveProfileService: ComprehensiveProfileService,
  ) {}

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Get current authenticated user\'s profile",
    description: "Retrieves the detailed profile information for the currently logged-in user."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Current user\'s profile retrieved successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User profile not found in the database. This might indicate an issue with data consistency or if the user was recently deleted.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async getMyProfile(@GetUser() user: PrismaUser): Promise<UserProfileResponseDto> { 
    const profile = await this.userService.findProfileById(user.id);
    if (!profile) {
      throw new NotFoundException('User profile not found. User might not exist in public table or JWT strategy is incorrect.');
    }
    return {
      id: profile.id,
      authUserId: profile.authUserId,
      email: profile.email,
      username: profile.username,
      displayName: profile.displayName,
      profilePictureUrl: profile.profilePictureUrl,
      bio: profile.bio,
      status: profile.status as UserStatus,
      role: profile.role as UserRole,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt,
      lastLoginAt: profile.lastLogin, 
    };
  }

  @Get('me/profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "Get comprehensive user profile",
    description: "Retrieves complete user profile including user data, preferences, statistics, and recent activity."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Comprehensive profile retrieved successfully.", type: ApiResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User profile not found.' })
  async getComprehensiveProfile(@GetUser() user: PrismaUser): Promise<ApiResponseDto<ComprehensiveProfileResponseDto>> {
    const profileData = await this.comprehensiveProfileService.getComprehensiveProfile(user.id);
    return ApiResponseDto.success(profileData);
  }

  @Put('me')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Update current authenticated user\'s profile",
    description: "Allows the currently logged-in user to update their profile information. Certain fields like email, role, or status might be restricted or handled by separate processes."
  })
  @ApiBody({ type: UpdateProfileDto })
  @ApiResponse({ status: HttpStatus.OK, description: "User profile updated successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., username format, length constraints) or username conflict if attempting to change to an existing username.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found during update attempt.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async updateMyProfile(
    @GetUser() user: PrismaUser,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<UserProfileResponseDto> { 
    const updatedProfile = await this.userService.updateProfile(user.id, updateProfileDto);
    return {
      id: updatedProfile.id,
      authUserId: updatedProfile.authUserId,
      email: updatedProfile.email,
      username: updatedProfile.username,
      displayName: updatedProfile.displayName,
      profilePictureUrl: updatedProfile.profilePictureUrl,
      bio: updatedProfile.bio,
      status: updatedProfile.status as UserStatus,
      role: updatedProfile.role as UserRole,
      createdAt: updatedProfile.createdAt,
      updatedAt: updatedProfile.updatedAt,
      lastLoginAt: updatedProfile.lastLogin,
    };
  }

  @Get('me/preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "Get current authenticated user\'s preferences",
    description: "Retrieves all user preferences including notifications, privacy, display, and content settings."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "User preferences retrieved successfully.", type: UserPreferencesResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async getMyPreferences(@GetUser() user: PrismaUser): Promise<UserPreferencesResponseDto> {
    return this.userPreferencesService.getUserPreferences(user.id);
  }

  @Put('me/preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "Update current authenticated user\'s preferences",
    description: "Allows the currently logged-in user to update their preferences including notifications, privacy, display, and content settings."
  })
  @ApiBody({ type: UpdateUserPreferencesDto })
  @ApiResponse({ status: HttpStatus.OK, description: "User preferences updated successfully.", type: UserPreferencesResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data for preferences.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async updateMyPreferences(
    @GetUser() user: PrismaUser,
    @Body() updateDto: UpdateUserPreferencesDto,
  ): Promise<UserPreferencesResponseDto> {
    return this.userPreferencesService.updateUserPreferences(user.id, updateDto);
  }

  @Get('me/tool-requests')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "Get current user's tool requests",
    description: "Retrieves all tool requests made by the currently logged-in user with pagination."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Tool requests retrieved successfully.", type: PaginatedToolRequestsResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async getMyToolRequests(
    @GetUser() user: PrismaUser,
    @Query(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
    listDto: ListToolRequestsDto,
  ): Promise<PaginatedToolRequestsResponseDto> {
    return this.toolRequestService.getUserToolRequests(user.id, listDto);
  }

  @Get('me/submitted-tools')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: "Get current user's submitted tools",
    description: "Retrieves all tools submitted by the currently logged-in user with their review status and entity details."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Submitted tools retrieved successfully.", type: PaginatedUserSubmittedToolsResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async getMySubmittedTools(
    @GetUser() user: PrismaUser,
    @Query(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
    listDto: ListUserSubmittedToolsDto,
  ): Promise<PaginatedUserSubmittedToolsResponseDto> {
    return this.userSubmittedToolsService.getUserSubmittedTools(user.id, listDto);
  }

  @Delete('me')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK) // Or 204 No Content if nothing is returned in body
  @ApiOperation({ 
    summary: "Soft delete current authenticated user\'s account",
    description: "Marks the current user\'s account for deletion. The actual deletion and data purging process might be asynchronous and subject to retention policies. This action requires the user\'s current JWT."
   })
  @ApiResponse({ status: HttpStatus.OK, description: 'Account successfully scheduled for deletion.', schema: { example: { message: 'Account successfully scheduled for deletion. ...'}} })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Failed to process account deletion due to an internal error.' })
  async softDeleteMyAccount(@GetUser() user: PrismaUser): Promise<{ message: string }> { 
    await this.userService.softDeleteUser(user.id, user.authUserId);
    return { message: 'Account successfully scheduled for deletion. All related data will be processed according to our retention policies.' };
  }

  // --- Placeholder CRUD generated by Nest CLI - Keep or remove as needed ---
  /*
  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    // Typically user creation is handled by AuthModule/Signup
    // return this.usersService.create(createUserDto);
  }

  @Get()
  findAll() {
    // Might be an admin endpoint
    // return this.usersService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    // Might be an admin endpoint or for viewing public profiles
    // return this.usersService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    // Might be an admin endpoint
    // return this.usersService.update(+id, updateUserDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    // Might be an admin endpoint
    // return this.usersService.remove(+id);
  }
  */
  // --- End Placeholder CRUD ---
}
