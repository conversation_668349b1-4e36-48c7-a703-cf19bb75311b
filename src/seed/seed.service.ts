import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EntityStatus } from '../../generated/prisma';
import { randomUUID } from 'crypto';

@Injectable()
export class SeedService {
  constructor(private readonly prisma: PrismaService) {}

  async seedAll() {
    try {
      console.log('🌱 Starting database seeding...');

      // Seed entity types
      const entityTypes = await this.seedEntityTypes();
      console.log(`✅ Created ${entityTypes.length} entity types`);

      // Seed app settings
      const appSettings = await this.seedAppSettings();
      console.log(`✅ Created ${appSettings.length} app settings`);

      // Seed categories if table exists
      let categories = [];
      try {
        categories = await this.seedCategories();
        console.log(`✅ Created ${categories.length} categories`);
      } catch (error) {
        console.log('ℹ️ Categories table not found, skipping...');
      }

      // Seed tags if table exists
      let tags = [];
      try {
        tags = await this.seedTags();
        console.log(`✅ Created ${tags.length} tags`);
      } catch (error) {
        console.log('ℹ️ Tags table not found, skipping...');
      }

      // Seed features if table exists
      let features = [];
      try {
        features = await this.seedFeatures();
        console.log(`✅ Created ${features.length} features`);
      } catch (error) {
        console.log('ℹ️ Features table not found, skipping...');
      }

      // Create admin user and sample entities
      const { adminUser, entities } = await this.seedSampleData();
      console.log(`✅ Created admin user and ${entities.length} sample entities`);

      return {
        success: true,
        message: 'Database seeded successfully!',
        data: {
          entityTypes: entityTypes.length,
          appSettings: appSettings.length,
          categories: categories.length,
          tags: tags.length,
          features: features.length,
          entities: entities.length,
          adminUser: adminUser ? 1 : 0,
        },
      };
    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      return {
        success: false,
        message: 'Database seeding failed',
        error: error.message,
      };
    }
  }

  async getStatus() {
    try {
      const counts = await Promise.all([
        this.prisma.entityType.count(),
        this.prisma.appSetting.count(),
        this.prisma.user.count(),
        this.prisma.entity.count(),
      ]);

      return {
        entityTypes: counts[0],
        appSettings: counts[1],
        users: counts[2],
        entities: counts[3],
      };
    } catch (error) {
      return {
        error: error.message,
      };
    }
  }

  private async seedEntityTypes() {
    const entityTypesData = [
      { name: 'AI Tool', description: 'Artificial Intelligence tools and platforms', slug: 'ai-tool' },
      { name: 'API', description: 'Application Programming Interfaces and web services', slug: 'api' },
      { name: 'Dataset', description: 'Data collections and datasets for training and research', slug: 'dataset' },
      { name: 'Model', description: 'Pre-trained machine learning models', slug: 'model' },
      { name: 'Platform', description: 'Development and deployment platforms', slug: 'platform' },
      { name: 'Library', description: 'Code libraries and frameworks', slug: 'library' },
      { name: 'Service', description: 'Cloud services and SaaS platforms', slug: 'service' },
    ];

    const results = [];
    for (const data of entityTypesData) {
      const result = await this.prisma.entityType.upsert({
        where: { name: data.name },
        update: {},
        create: data,
      });
      results.push(result);
    }
    return results;
  }

  private async seedAppSettings() {
    const settingsData = [
      { key: 'default_llm_provider', value: 'openai', description: 'Default LLM provider for the application' },
      { key: 'max_entities_per_page', value: '20', description: 'Maximum number of entities to show per page' },
      { key: 'enable_user_submissions', value: 'true', description: 'Allow users to submit new entities' },
      { key: 'enable_tool_requests', value: 'true', description: 'Allow users to request new tools' },
      { key: 'maintenance_mode', value: 'false', description: 'Enable maintenance mode' },
      { key: 'featured_entities_count', value: '6', description: 'Number of featured entities to show on homepage' },
      { key: 'enable_reviews', value: 'true', description: 'Allow users to write reviews' },
      { key: 'min_review_length', value: '50', description: 'Minimum review length in characters' },
    ];

    const results = [];
    for (const data of settingsData) {
      const result = await this.prisma.appSetting.upsert({
        where: { key: data.key },
        update: {},
        create: data,
      });
      results.push(result);
    }
    return results;
  }

  private async seedCategories() {
    const categoriesData = [
      { name: 'Machine Learning', description: 'Tools and platforms for machine learning', slug: 'machine-learning' },
      { name: 'Natural Language Processing', description: 'NLP tools and language models', slug: 'natural-language-processing' },
      { name: 'Computer Vision', description: 'Image and video processing tools', slug: 'computer-vision' },
      { name: 'Data Science', description: 'Data analysis and visualization tools', slug: 'data-science' },
      { name: 'Development Tools', description: 'Developer tools and IDEs', slug: 'development-tools' },
      { name: 'Automation', description: 'Workflow automation and productivity tools', slug: 'automation' },
      { name: 'Analytics', description: 'Business intelligence and analytics platforms', slug: 'analytics' },
      { name: 'Content Creation', description: 'Tools for creating and editing content', slug: 'content-creation' },
      { name: 'Research', description: 'Academic and research tools', slug: 'research' },
      { name: 'Business Intelligence', description: 'BI and enterprise analytics tools', slug: 'business-intelligence' },
    ];

    const results = [];
    for (const data of categoriesData) {
      const result = await this.prisma.category.upsert({
        where: { name: data.name },
        update: {},
        create: data,
      });
      results.push(result);
    }
    return results;
  }

  private async seedTags() {
    const tagsData = [
      { name: 'Free', description: 'Free to use tools and services', slug: 'free' },
      { name: 'Open Source', description: 'Open source projects and tools', slug: 'open-source' },
      { name: 'API Available', description: 'Tools that provide APIs', slug: 'api-available' },
      { name: 'Cloud-based', description: 'Cloud-based services', slug: 'cloud-based' },
      { name: 'Enterprise', description: 'Enterprise-grade solutions', slug: 'enterprise' },
      { name: 'Beginner Friendly', description: 'Easy to use for beginners', slug: 'beginner-friendly' },
      { name: 'Advanced', description: 'Advanced tools for experts', slug: 'advanced' },
      { name: 'No-Code', description: 'No coding required', slug: 'no-code' },
      { name: 'Real-time', description: 'Real-time processing capabilities', slug: 'real-time' },
      { name: 'Mobile App', description: 'Has mobile application', slug: 'mobile-app' },
    ];

    const results = [];
    for (const data of tagsData) {
      const result = await this.prisma.tag.upsert({
        where: { name: data.name },
        update: {},
        create: data,
      });
      results.push(result);
    }
    return results;
  }

  private async seedFeatures() {
    const featuresData = [
      { name: 'Text Generation', description: 'Generate human-like text content', slug: 'text-generation' },
      { name: 'Image Generation', description: 'Create images from text descriptions', slug: 'image-generation' },
      { name: 'Code Generation', description: 'Generate code in various programming languages', slug: 'code-generation' },
      { name: 'Data Analysis', description: 'Analyze and visualize data', slug: 'data-analysis' },
      { name: 'Translation', description: 'Translate text between languages', slug: 'translation' },
      { name: 'Summarization', description: 'Summarize long text content', slug: 'summarization' },
      { name: 'Question Answering', description: 'Answer questions based on context', slug: 'question-answering' },
      { name: 'Speech Recognition', description: 'Convert speech to text', slug: 'speech-recognition' },
      { name: 'Text-to-Speech', description: 'Convert text to speech', slug: 'text-to-speech' },
      { name: 'Sentiment Analysis', description: 'Analyze sentiment in text', slug: 'sentiment-analysis' },
    ];

    const results = [];
    for (const data of featuresData) {
      const result = await this.prisma.feature.upsert({
        where: { name: data.name },
        update: {},
        create: data,
      });
      results.push(result);
    }
    return results;
  }

  private async seedSampleData() {
    // Create admin user
    const adminUser = await this.prisma.user.upsert({
      where: { username: 'admin' },
      update: {},
      create: {
        authUserId: randomUUID(), // Generate proper UUID
        username: 'admin',
        displayName: 'System Administrator',
        email: '<EMAIL>',
        role: 'ADMIN',
        status: 'ACTIVE',
      },
    });

    // Get entity types
    const aiToolType = await this.prisma.entityType.findFirst({ where: { slug: 'ai-tool' } });
    const platformType = await this.prisma.entityType.findFirst({ where: { slug: 'platform' } });
    const apiType = await this.prisma.entityType.findFirst({ where: { slug: 'api' } });

    if (!aiToolType || !platformType || !apiType) {
      throw new Error('Required entity types not found. Please seed entity types first.');
    }

    // Create sample entities
    const entitiesData = [
      {
        name: 'ChatGPT',
        slug: 'chatgpt',
        websiteUrl: 'https://chat.openai.com',
        entityTypeId: aiToolType.id,
        shortDescription: 'AI-powered conversational assistant by OpenAI',
        description: 'ChatGPT is a large language model developed by OpenAI that can engage in conversational interactions, answer questions, help with writing, coding, analysis, and many other tasks.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
      {
        name: 'Claude',
        slug: 'claude',
        websiteUrl: 'https://claude.ai',
        entityTypeId: aiToolType.id,
        shortDescription: 'AI assistant by Anthropic focused on helpful, harmless, and honest interactions',
        description: 'Claude is an AI assistant created by Anthropic. It is designed to be helpful, harmless, and honest, with strong capabilities in analysis, writing, math, coding, and creative tasks.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
      {
        name: 'Midjourney',
        slug: 'midjourney',
        websiteUrl: 'https://midjourney.com',
        entityTypeId: aiToolType.id,
        shortDescription: 'AI-powered image generation tool',
        description: 'Midjourney is an independent research lab that produces an AI program that creates images from textual descriptions. It is known for its artistic and creative image generation capabilities.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
      {
        name: 'GitHub Copilot',
        slug: 'github-copilot',
        websiteUrl: 'https://github.com/features/copilot',
        entityTypeId: aiToolType.id,
        shortDescription: 'AI pair programmer that helps you write code faster',
        description: 'GitHub Copilot is an AI coding assistant that provides autocomplete-style suggestions as you code. It is trained on billions of lines of code and can help with various programming languages.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
      {
        name: 'Hugging Face',
        slug: 'hugging-face',
        websiteUrl: 'https://huggingface.co',
        entityTypeId: platformType.id,
        shortDescription: 'The AI community building the future of machine learning',
        description: 'Hugging Face is a platform that provides tools and resources for machine learning, including pre-trained models, datasets, and spaces for hosting ML applications.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
      {
        name: 'OpenAI API',
        slug: 'openai-api',
        websiteUrl: 'https://platform.openai.com',
        entityTypeId: apiType.id,
        shortDescription: 'Access to OpenAI\'s powerful AI models via API',
        description: 'The OpenAI API provides access to powerful AI models including GPT-4, GPT-3.5, DALL-E, and Whisper through a simple REST API interface.',
        status: EntityStatus.ACTIVE,
        submitterId: adminUser.id,
      },
    ];

    const entities = [];
    for (const data of entitiesData) {
      const entity = await this.prisma.entity.upsert({
        where: { name: data.name },
        update: {},
        create: data,
      });
      entities.push(entity);
    }

    return { adminUser, entities };
  }

  async seedEntitiesOnly() {
    try {
      console.log('🌱 Creating sample entities only...');

      // Get or create admin user
      let adminUser = await this.prisma.user.findFirst({ where: { username: 'admin' } });
      if (!adminUser) {
        adminUser = await this.prisma.user.create({
          data: {
            authUserId: randomUUID(),
            username: 'admin',
            displayName: 'System Administrator',
            email: '<EMAIL>',
            role: 'ADMIN',
            status: 'ACTIVE',
          },
        });
      }

      // Get entity types
      const aiToolType = await this.prisma.entityType.findFirst({ where: { slug: 'ai-tool' } });
      if (!aiToolType) {
        throw new Error('AI Tool entity type not found. Please run seed/all first.');
      }

      // Create one simple entity
      const entity = await this.prisma.entity.create({
        data: {
          name: 'ChatGPT',
          slug: 'chatgpt',
          websiteUrl: 'https://chat.openai.com',
          entityTypeId: aiToolType.id,
          shortDescription: 'AI-powered conversational assistant by OpenAI',
          description: 'ChatGPT is a large language model developed by OpenAI that can engage in conversational interactions.',
          status: EntityStatus.ACTIVE,
          submitterId: adminUser.id,
        },
      });

      return {
        success: true,
        message: 'Sample entity created successfully!',
        data: {
          entity: entity.name,
          adminUser: adminUser.username,
        },
      };
    } catch (error) {
      console.error('❌ Entity creation failed:', error);
      return {
        success: false,
        message: 'Entity creation failed',
        error: error.message,
      };
    }
  }

  async checkSchema() {
    try {
      // Check what tables exist
      const tables = await this.prisma.$queryRaw`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
      `;

      // Check entities table columns
      const entityColumns = await this.prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'entities' AND table_schema = 'public'
        ORDER BY ordinal_position
      `;

      return {
        tables,
        entityColumns,
      };
    } catch (error) {
      return {
        error: error.message,
      };
    }
  }
}
