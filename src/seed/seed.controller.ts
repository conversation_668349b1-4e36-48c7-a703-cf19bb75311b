import { Controller, Post, Get } from '@nestjs/common';
import { SeedService } from './seed.service';

@Controller('seed')
export class SeedController {
  constructor(private readonly seedService: SeedService) {}

  @Post('all')
  async seedAll() {
    return this.seedService.seedAll();
  }

  @Get('status')
  async getStatus() {
    return this.seedService.getStatus();
  }

  @Post('entities-only')
  async seedEntitiesOnly() {
    return this.seedService.seedEntitiesOnly();
  }

  @Get('schema')
  async checkSchema() {
    return this.seedService.checkSchema();
  }
}
